## Copilot 项目专用指令 (AI 开发速览)

> 目标：### 5. 常见修改指引

- 新增模型提供商：扩展 `core/services/model/*` 配置 + LLM 调用分支；保证 llmParams "直通"+ 文档补充（`docs/developer/llm-params-guide.md`）。
- 新增提示词优化策略：在 `core/services/prompt/service.ts` 添加方法 → 导出 → web 接线（UI 通过组合式函数/组件调用）。
- 修改代理行为：只动 `api/proxy.js` / `api/stream.js`，保持无业务判断，可加 headers 过滤或流健壮性。

### 6. CI / 发布触发点（避免误触）

- 推送到 `main/master` 触发：测试 + 构建 + Docker（`docker.yml`）、Vercel（外部配置）。
- 文档 / `docs/**` 变更不会触发 test/docker workflows（paths-ignore）。

### 7. 代码与测试风格

- TypeScript：严格避免 `any`（如果上游 SDK 松散，使用窄化或自定义类型扩展）。本仓库的架构、关键工作流与约定，避免通用/臆测实践，仅聚焦已经实现的模式。约 40 行内精炼，后续可增量改进。

### 1. 架构总览

- Monorepo (pnpm workspace)；核心逻辑集中在 `packages/core`，界面/分发形态：`web`、`desktop`(Electron)、`extension`(Chrome)、`mcp-server`、共享组件 `ui`。
- 依赖方向：core → (ui) → web/extension/desktop；mcp-server 仅调用 core 暴露的服务 API（零侵入）。
- 运行形态：
  - 纯前端（默认）——浏览器直连模型服务（需 CORS OK）。
  - Vercel 部署：通过 `api/proxy.js` 与 `api/stream.js` 可选代理（模型配置里 `useVercelProxy`）。
  - Docker 镜像整合静态站点 + MCP 入口 `/mcp`。

### 2. 关键包角色

- `packages/core/src/services/`：LLM 调用、模型配置管理（`model/manager.ts`）、提示词优化（`prompt/service.ts`）、模板与历史管理。修改逻辑优先在这里集中，保持 Web 纯 UI/交互。
- `packages/web`：Vue 3 + Tailwind 应用层，组件/业务模式以组合式函数 + 轻服务封装；保持对 core 的瘦封装。
- `api/`：Vercel Edge 代理与流式转发；保持无状态，不做业务分支。

### 3. 开发 / 构建工作流（脚本优先使用现有命令）

- 安装：`pnpm install`；常规开发：`pnpm dev` (并行 watch core/ui + web)。
- 全量构建顺序由脚本保证：`pnpm build` → core/ui → web。
- 测试：`pnpm test`（递归运行各包 Vitest，允许无测试通过）。单包：`pnpm -F @prompt-optimizer/core test`。
- 版本同步：修改根 `package.json` 版本后运行 `pnpm run version:sync`（会更新扩展 manifest、desktop 包）。

### 4. 项目特有模式 / 需要遵守的实现细节

- LLM 参数透传：`ModelConfig.llmParams` 不设默认值；仅对 OpenAI 兼容系解释 `timeout` 并传入客户端，其余原样传给 `chat.completions.create`；Gemini 构建 `generationConfig` 时仅包含用户显式设置的字段。新增 provider 时保持“无隐式默认”。
- 代理判定：仅在非 localhost 且用户勾选时经 `/api/proxy` 或 `/api/stream`；不要在核心服务里写死代理逻辑，扩展在环境工具/模型配置层。
- 流式处理：核心层负责拆分/组装事件 → UI 层逐步渲染；不要在组件中混入协议解析。
- 错误处理：core 服务定义专用错误（如 `errors.ts`）；UI 捕获后转为用户友好提示，不向上抛未分类原始错误。
- MCP 零侵入：禁止从 mcp-server 反向 import web/ui；新增工具 = 封装 core 已有纯函数/服务方法。

### 5. 常见修改指引

- 新增模型提供商：扩展 `core/services/model/*` 配置 + LLM 调用分支；保证 llmParams “直通”+ 文档补充（`docs/developer/llm-params-guide.md`）。
- 新增提示词优化策略：在 `core/services/prompt/service.ts` 添加方法 → 导出 → web & mcp 各自接线（UI 通过组合式函数/组件调用；mcp 在 tool registry 里映射）。
- 调整桌面打包：改动仅限 `packages/desktop`，必要时同步 `scripts/sync-versions.js` 列表。
- 修改代理行为：只动 `api/proxy.js` / `api/stream.js`，保持无业务判断，可加 headers 过滤或流健壮性。

### 6. CI / 发布触发点（避免误触）

- 推送到 `main/master` 触发：测试 + 构建 + Docker（`docker.yml`）、Vercel（外部配置）。
- 推送 Tag 触发桌面发布（release workflow）；若只是本地验证，不要创建 Tag。
- 文档 / `docs/**` 变更不会触发 test/docker workflows（paths-ignore）。

### 7. 代码与测试风格

- TypeScript：严格避免 `any`（如果上游 SDK 松散，使用窄化或自定义类型扩展）。
- Vue 组件：`<script setup>` + PascalCase 文件名；逻辑重用走 `composables/`。
- 测试：Vitest；新服务先写最小 happy path + 失败分支；流式逻辑用可控 mock 代替真实网络。

### 8. 安全 / 配置注意

- 所有密钥仅在客户端/桌面/扩展本地存储加密，不落服务；勿添加服务端持久化。
- 环境变量命名保持前缀：运行时代码访问的需 `VITE_`（前端可见）；新增敏感变量前确认是否必须暴露。

### 9. AI 代理务必避免

- 不要引入与现有 SDK 重复的抽象层或 HTTP 客户端替换。
- 不要擅自添加默认 LLM 参数或把“缺省值”写入用户配置。
- 不要在 mcp-server 中复制 core 逻辑（只调用）。
- 不要修改 CI 触发分支/忽略规则，除非需求明确说明。

### 10. 快速示例（扩展一个新 MCP 工具）

1. 在 core 添加纯函数 `optimizeXPrompt(...)`。
2. 导出于 core `index.ts`。
3. 在 `packages/mcp-server/src/tools/` 新建 tool 定义，调用 core 函数返回结构化结果；遵循现有三个工具返回格式（参考 `README.md` 工具列表）。
4. 添加最小 Vitest：调用函数 + 模拟输入 + 断言输出结构。

—— 若以上某部分不充分，请反馈具体条目，我会增量补充。
