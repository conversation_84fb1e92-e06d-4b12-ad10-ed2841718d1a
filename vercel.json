{"buildCommand": "pnpm build", "outputDirectory": "packages/web/dist", "installCommand": "pnpm install && pnpm i @vercel/analytics -w", "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}, {"source": "/(.*)", "destination": "/index.html"}], "github": {"silent": true}, "git": {"deploymentEnabled": {"main": true, "master": true}}, "env": {"VITE_VERCEL_DEPLOYMENT": "true"}, "build": {"env": {"VITE_VERCEL_DEPLOYMENT": "true"}}}