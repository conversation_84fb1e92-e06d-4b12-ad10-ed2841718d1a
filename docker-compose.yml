version: '3.8'

services:
  prompt-optimizer:
    image: linshen/prompt-optimizer:latest
    #     Alternatively, you can build from source:
    #     build:
    #        context: .
    #        dockerfile: Dockerfile
    container_name: prompt-optimizer
    restart: unless-stopped
    ports:
      - '8081:${NGINX_PORT:-80}' # Web应用端口（包含MCP服务器，通过/mcp路径访问）
    healthcheck:
      test: ['CMD', 'sh', '-c', 'curl -f http://localhost:${NGINX_PORT:-80}/ && curl -f http://localhost:${NGINX_PORT:-80}/mcp']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    environment:
      # nginx内部端口配置
      - NGINX_PORT=${NGINX_PORT:-80}

      # Web应用API配置
      - VITE_OPENAI_API_KEY=${VITE_OPENAI_API_KEY:-}
      - VITE_GEMINI_API_KEY=${VITE_GEMINI_API_KEY:-}
      - VITE_DEEPSEEK_API_KEY=${VITE_DEEPSEEK_API_KEY:-}
      - VITE_SILICONFLOW_API_KEY=${VITE_SILICONFLOW_API_KEY:-}
      - VITE_CUSTOM_API_KEY=${VITE_CUSTOM_API_KEY:-}
      - VITE_CUSTOM_API_BASE_URL=${VITE_CUSTOM_API_BASE_URL:-}
      - VITE_CUSTOM_API_MODEL=${VITE_CUSTOM_API_MODEL:-}

      # MCP服务器配置（Docker内部固定端口3000，不使用MCP_HTTP_PORT）
      # - MCP_HTTP_PORT=${MCP_HTTP_PORT:-3000}  # Docker中固定使用3000端口
      - MCP_LOG_LEVEL=${MCP_LOG_LEVEL:-debug}
      - MCP_DEFAULT_LANGUAGE=${MCP_DEFAULT_LANGUAGE:-zh}
      - MCP_DEFAULT_MODEL_PROVIDER=${MCP_DEFAULT_MODEL_PROVIDER:-openai}

      # Basic认证配置（可选）
      - ACCESS_USERNAME=${ACCESS_USERNAME:-admin}
      - ACCESS_PASSWORD=${ACCESS_PASSWORD:-}
    security_opt:
      - no-new-privileges:true
