{"folder-color.pathColors": [{"folderPath": "ele-autotesting/vercel.json/", "color": "foldercolorizer.color_ff0000"}, {"folderPath": "ele-autotesting/pnpm-workspace.yaml/", "color": "foldercolorizer.color_ff0000"}, {"folderPath": "ele-autotesting/package.json/", "color": "foldercolorizer.color_ff0000"}, {"folderPath": "ele-autotesting/node-proxy/", "color": "foldercolorizer.color_ff0000"}, {"folderPath": "ele-autotesting/api/", "color": "foldercolorizer.color_ff0000"}], "chatgpt.openOnStartup": false, "i18n-ally.localesPaths": ["packages/ui/src/i18n", "packages/ui/src/i18n/locales"], "i18n-ally.enabledFrameworks": ["vue"], "i18n-ally.keystyle": "nested", "i18n-ally.sourceLanguage": "zh-CN", "i18n-ally.displayLanguage": "zh-CN", "i18n-ally.enabledParsers": ["ts", "json", "yaml"], "i18n-ally.pathMatcher": "{locale}.ts", "i18n-ally.annotationInPlace": true}