# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Prompt Optimizer** is an AI prompt optimization tool designed to help users write better AI prompts and improve AI output quality. It's built as a monorepo supporting multiple platforms: Web, Desktop (Electron), Chrome Extension, MCP Server, and Docker deployment.

## Key Commands

### Development

```bash
# Development setup
pnpm install                  # Install dependencies
pnpm dev                     # Start web development (builds core/ui then runs web)
pnpm dev:fresh               # Full reset web development
pnpm dev:desktop             # Desktop development (builds core/ui/web then runs desktop)
pnpm dev:desktop:fresh       # Full reset desktop development

# Package-specific development
pnpm -F @prompt-optimizer/core build --watch
pnpm -F @prompt-optimizer/ui build --watch
pnpm -F @prompt-optimizer/web dev
```

### Testing

```bash
pnpm test                    # Run all tests across packages
pnpm -F @prompt-optimizer/core test     # Test core package
pnpm -F @prompt-optimizer/ui test       # Test UI package
```

### Building

```bash
pnpm build                   # Build all packages (core → ui → web/ext/desktop parallel)
pnpm build:core              # Build core package only
pnpm build:ui                # Build UI package only
pnpm build:web               # Build web application
pnpm build:ext               # Build Chrome extension
pnpm build:desktop           # Build desktop application (includes packaging)
```

### Version Management

```bash
pnpm version:prepare patch   # Update version (1.0.0 → 1.0.1)
pnpm version:prepare minor   # Update version (1.0.0 → 1.1.0)
pnpm run version:tag         # Create git tag with current version
pnpm run version:publish     # Push git tag (triggers desktop release)
```

### Utilities

```bash
pnpm clean                   # Clean all dist directories
pnpm clean:vite              # Clean Vite cache
pnpm pnpm-install            # Reinstall dependencies
```

## Architecture Overview

### Monorepo Structure

The project uses pnpm workspaces with the following packages:

```
packages/
├── core/           # Core business logic and services (@prompt-optimizer/core)
├── ui/             # Reusable Vue components (@prompt-optimizer/ui)
└── web/            # Web application (@prompt-optimizer/web)
```

### Dependency Graph

- **Core**: Independent, provides services and utilities
- **UI**: Depends on Core, provides Vue components
- **Web**: Depends on Core + UI

### Build Process

1. **Sequential**: Core → UI (must be built first)
2. **Parallel**: Web, Desktop, Extension (can build simultaneously)
3. **Desktop packaging**: Includes Electron app creation with auto-updater

### Key Services (packages/core/src/services/)

- **LLM Service**: Handles AI model communications (OpenAI, Gemini, DeepSeek, etc.)
- **Model Manager**: Manages AI model configurations and settings
- **Prompt Service**: Core prompt optimization logic
- **Template Manager**: Manages prompt templates with i18n support
- **History Manager**: Handles optimization history
- **Storage Service**: Abstracted storage (localStorage, IndexedDB, file system)

## Development Guidelines

### Code Standards

- **TypeScript**: Strict typing required
- **Vue 3**: Composition API preferred
- **Testing**: Unit tests required for core services
- **Error Handling**: Comprehensive error handling with user-friendly messages

### File Organization

- Services follow pattern: `service.ts`, `types.ts`, `errors.ts`
- Each service has corresponding tests in `tests/unit/` or `tests/integration/`
- UI components are self-contained with associated composables

### Key Patterns

- **Service Layer**: Business logic abstracted into core services
- **Composables**: Vue composition functions for UI state management
- **Storage Abstraction**: Multiple storage backends with unified interface
- **Template Processing**: CSP-safe template processing for security
- **Error Boundaries**: Graceful error handling throughout the stack

## Special Considerations

### Cross-Origin Issues

The project handles CORS limitations for browser-based API calls:

- Vercel proxy available for web deployment
- Desktop app bypasses CORS restrictions
- Extension has limited CORS bypass capabilities

### Security

- **CSP Compliance**: All template processing is CSP-safe
- **API Key Security**: Keys stored locally, never transmitted to project servers
- **Input Validation**: Comprehensive validation for user inputs

### Internationalization

- **i18n**: Full Chinese/English support
- **Template Localization**: Templates available in multiple languages
- **Dynamic Language Switching**: Runtime language switching

### Platform-Specific Features

- **Desktop**: File system storage, auto-updates, full API access
- **Web**: LocalStorage/IndexedDB, CORS limitations
- **Extension**: Limited storage, popup interface
- **MCP Server**: Protocol-compliant server for Claude Desktop integration

## Important Files

- `packages/core/src/services/` - All core business logic
- `packages/ui/src/composables/` - Vue composition functions
- `docs/developer/` - Detailed technical documentation
- `docs/archives/` - Implementation history and learnings
- `.cursor/rules/` - Development assistant rules (Vue.js expert, Web development mentor)

When working on this codebase, always run tests after changes, follow the established service patterns, and ensure cross-platform compatibility.
