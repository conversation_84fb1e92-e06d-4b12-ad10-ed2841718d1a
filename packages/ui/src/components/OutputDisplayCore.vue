<template>
  <div class="output-display-core theme-card flex flex-col h-full relative !p-0" :class="displayClasses">
    <!-- 统一顶层工具栏 -->
    <div
      v-if="hasToolbar"
      data-testid="output-display-toolbar"
      class="theme-toolbar-bg flex items-center justify-between px-3 py-2 border-b"
      :class="themeToolbarBorder"
    >
      <!-- 左侧：视图控制按钮组 -->
      <div class="flex items-center border rounded-md" :class="themeToolbarBorder">
        <button
          @click="internalViewMode = 'render'"
          :disabled="internalViewMode === 'render'"
          class="px-3 py-1.5 text-sm rounded-r-none border-r"
          :class="[themeToolbarButton, themeToolbarBorder, { [themeToolbarButtonActive]: internalViewMode === 'render' }]"
        >
          {{ t('common.render') }}
        </button>
        <button
          @click="internalViewMode = 'source'"
          :disabled="internalViewMode === 'source'"
          class="px-3 py-1.5 text-sm rounded-none"
          :class="[themeToolbarButton, { [themeToolbarButtonActive]: internalViewMode === 'source' }]"
        >
          {{ t('common.source') }}
        </button>
        <button
          v-if="isActionEnabled('diff') && originalContent"
          @click="internalViewMode = 'diff'"
          :disabled="internalViewMode === 'diff' || !originalContent"
          :title="!originalContent ? t('messages.noOriginalContentForDiff') : ''"
          class="px-3 py-1.5 text-sm rounded-l-none border-l"
          :class="[themeToolbarButton, themeToolbarBorder, { [themeToolbarButtonActive]: internalViewMode === 'diff' }]"
        >
          {{ t('common.compare') }}
        </button>
      </div>

      <!-- 右侧：操作按钮 -->
      <!-- TODO: 添加下载 excel 的一个按钮 -->
      <div class="flex items-center gap-2">
        <button v-if="isActionEnabled('excel')" @click="handleExcel" class="theme-icon-button" :title="t('actions.excel')">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
        </button>
        <button v-if="isActionEnabled('copy')" @click="handleCopy('content')" class="theme-icon-button" :title="t('actions.copy')">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.03 1.125 0 1.13.094 1.976 1.057 1.976 2.192V7.5M8.25 7.5h7.5M8.25 7.5h-1.5a1.5 1.5 0 00-1.5 1.5v11.25c0 .828.672 1.5 1.5 1.5h10.5a1.5 1.5 0 001.5-1.5V9a1.5 1.5 0 00-1.5-1.5h-1.5"
            />
          </svg>
        </button>
        <button v-if="isActionEnabled('fullscreen')" @click="handleFullscreen" class="theme-icon-button" :title="t('actions.fullscreen')">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            />
          </svg>
        </button>
      </div>
    </div>

    <!-- 推理内容区域 -->
    <div v-if="shouldShowReasoning">
      <!-- 推理面板标题栏 -->
      <div
        class="reasoning-header flex items-center justify-between px-3 py-2 border-b cursor-pointer theme-toolbar-bg theme-toolbar-hover-bg"
        :class="themeToolbarBorder"
        @click="toggleReasoning"
      >
        <span class="text-sm font-medium theme-label">
          {{ t('common.reasoning') }}
        </span>
        <div class="flex items-center gap-2">
          <div v-if="isReasoningStreaming" class="streaming-indicator">
            <span class="text-xs">{{ t('common.generating') }}</span>
          </div>
          <svg
            class="reasoning-toggle w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': isReasoningExpanded }"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <polyline points="6,9 12,15 18,9"></polyline>
          </svg>
        </div>
      </div>

      <!-- 推理内容区 -->
      <div v-if="isReasoningExpanded" class="output-display__reasoning" :class="{ streaming: streaming }">
        <div class="reasoning-content" ref="reasoningContentRef">
          <MarkdownRenderer
            v-if="displayReasoning"
            :content="displayReasoning"
            :streaming="streaming"
            class="theme-markdown-content prose-sm max-w-none px-3 py-2"
          />
          <div v-else-if="streaming" class="text-gray-500 text-sm italic px-3 py-2">
            {{ t('common.generatingReasoning') }}
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="output-display__content flex flex-col" :style="{ height: computedHeight }">
      <!-- 对比模式 -->
      <TextDiffUI
        v-if="internalViewMode === 'diff' && content && originalContent"
        :originalText="originalContent"
        :optimizedText="content"
        :compareResult="compareResult"
        :isEnabled="true"
        :showHeader="false"
        displayMode="optimized"
        class="w-full flex-1 min-h-0"
      />

      <!-- 原文模式 -->
      <div v-else-if="internalViewMode === 'source'" class="h-full">
        <textarea
          :value="content"
          @input="handleSourceInput"
          :readonly="mode !== 'editable' || streaming"
          class="w-full h-full theme-input resize-none px-3 py-2 !border-none !shadow-none"
          :placeholder="placeholder"
        ></textarea>
      </div>

      <!-- 渲染模式（默认） -->
      <div
        v-else
        class="h-full overflow-auto"
        :class="isEmpty ? 'theme-input !border-none !shadow-none !p-0' : 'theme-content-container !border-none !shadow-none'"
      >
        <MarkdownRenderer v-if="displayContent" :content="displayContent" :streaming="streaming" class="px-3 py-2" />
        <div v-else-if="loading || streaming" class="loading-placeholder px-3 py-2">
          {{ placeholder || t('common.loading') }}
        </div>
        <div v-else class="empty-placeholder px-3 py-2">
          {{ placeholder || t('common.noContent') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useClipboard } from '../composables/useClipboard'
import { useToast } from '../composables/useToast'
import MarkdownRenderer from './MarkdownRenderer.vue'
import TextDiffUI from './TextDiff.vue'
import type { CompareResult, ICompareService } from '@prompt-optimizer/core'
import writeXlsxFile from 'write-excel-file'

type ActionName = 'fullscreen' | 'diff' | 'copy' | 'edit' | 'reasoning' | 'excel'

const { t } = useI18n()
const { copyText } = useClipboard()
const toast = useToast()

// 组件 Props
interface Props {
  // 内容相关
  content?: string
  originalContent?: string
  reasoning?: string

  // 显示模式
  mode: 'readonly' | 'editable'
  reasoningMode?: 'show' | 'hide' | 'auto'

  // 功能开关
  enabledActions?: ActionName[]

  // 样式配置
  height?: string | number
  placeholder?: string

  // 状态
  loading?: boolean
  streaming?: boolean

  // 服务
  compareService: ICompareService
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  originalContent: '',
  reasoning: '',
  mode: 'readonly',
  reasoningMode: 'auto',
  enabledActions: () => ['fullscreen', 'diff', 'copy', 'edit', 'reasoning', 'excel'],
  height: '100%',
  placeholder: '',
})

// 事件定义
const emit = defineEmits<{
  'update:content': [content: string]
  'update:reasoning': [reasoning: string]
  copy: [content: string, type: 'content' | 'reasoning' | 'all']
  fullscreen: []
  'edit-start': []
  'edit-end': []
  'reasoning-toggle': [expanded: boolean]
  'view-change': [mode: 'base' | 'diff']
}>()

// 内部状态
const isReasoningExpanded = ref(false)
const reasoningContentRef = ref<HTMLDivElement | null>(null)
const userHasManuallyToggledReasoning = ref(false)

// 新的视图状态机
const internalViewMode = ref<'render' | 'source' | 'diff'>('render')
const compareResult = ref<CompareResult | undefined>()

const isActionEnabled = (action: ActionName) => props.enabledActions.includes(action)

const hasToolbar = computed(() => ['diff', 'copy', 'fullscreen', 'edit', 'excel'].some((action) => isActionEnabled(action as ActionName)))

// 计算属性
const displayContent = computed(() => (props.content || '').trim())
const displayReasoning = computed(() => (props.reasoning || '').trim())

const hasContent = computed(() => !!displayContent.value)
const hasReasoning = computed(() => !!displayReasoning.value)

const isReasoningStreaming = computed(() => {
  // isReasoningStreaming 应该精确地表示"思考过程正在流式输出，而主内容尚未开始"
  return props.streaming && hasReasoning.value && !hasContent.value
})

const shouldShowReasoning = computed(() => {
  if (!isActionEnabled('reasoning')) return false
  if (props.reasoningMode === 'hide') return false
  if (props.reasoningMode === 'show') return true
  // 只有在有实际的思考内容时，才应该显示整个区域
  return hasReasoning.value
})

const isEmpty = computed(() => !hasContent.value && !props.loading && !props.streaming)

const displayClasses = computed(() => ({
  'output-display-core--loading': props.loading,
  'output-display-core--streaming': props.streaming,
}))

const computedHeight = computed(() => {
  if (typeof props.height === 'number') {
    return `${props.height}px`
  }
  return props.height
})

// 处理原文模式输入
const handleSourceInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  emit('update:content', target.value)
}

// 下载 excel 功能
type TestCaseRecord = {
  name: string
  preconditions: string
  module: string
  steps: string
  expected: string
  tags: string
  level: string
}

// 移除首尾 ``` 代码块包裹（如果存在）
const stripCodeFences = (raw: string): string => {
  if (!raw) return ''
  const lines = raw.replace(/\r\n?/g, '\n').split('\n')
  // 仅当第一行是 ``` 开头时，移除首行
  if (lines.length > 0 && /^\s*```/.test(lines[0])) {
    lines.shift()
  }
  // 仅当最后一行是 ``` 时，移除末行
  if (lines.length > 0 && /^\s*```\s*$/.test(lines[lines.length - 1])) {
    lines.pop()
  }
  return lines.join('\n').trim()
}

// 将全文切割为多个区块。以单独一行的 --- 作为分隔
const splitBlocks = (text: string): string[] => {
  const lines = text.split('\n')
  const blocks: string[] = []
  let buf: string[] = []
  const isSeparator = (line: string) => /^\s*-{3,}\s*$/.test(line)
  for (const line of lines) {
    if (isSeparator(line)) {
      const chunk = buf.join('\n').trim()
      if (chunk) blocks.push(chunk)
      buf = []
    } else {
      buf.push(line)
    }
  }
  const last = buf.join('\n').trim()
  if (last) blocks.push(last)
  return blocks.length ? blocks : [text.trim()].filter(Boolean)
}

// 别名映射，尽量覆盖常见写法
const LABEL_ALIASES: Record<keyof TestCaseRecord, string[]> = {
  name: ['用例名称', '用例名', '标题', '用例标题'],
  preconditions: ['前置条件', '前提条件', '先决条件'],
  module: ['所属模块', '模块', '模块路径', '所属目录'],
  steps: ['步骤描述', '步骤', '测试步骤', '操作步骤'],
  expected: ['预期结果', '期望结果', '预期', '期望'],
  tags: ['标签', 'Tags', 'Tag', '标记'],
  level: ['用例等级', '等级', '优先级', '优先级别', '用例级别', '级别', 'P级'],
}

const aliasToCanonical = (() => {
  const map = new Map<string, keyof TestCaseRecord>()
  ;(Object.keys(LABEL_ALIASES) as (keyof TestCaseRecord)[]).forEach((key) => {
    LABEL_ALIASES[key].forEach((alias) => map.set(alias.toLowerCase(), key))
  })
  return map
})()

// 构建用于匹配“标签: 值”的正则（锚定在行首，冒号支持中英文）
const buildLabelRegex = () => {
  const allLabels = Array.from(aliasToCanonical.keys())
    // 优先匹配更长的别名，避免如“模块”先匹配到而吞掉“所属模块”
    .sort((a, b) => b.length - a.length)
    .map((l) => l.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
    .join('|')
  // (^|\n) 使其只能在行首出现，避免值中误匹配
  return new RegExp(`(^|\n)\s*(${allLabels})\s*[:：]`, 'gi')
}

const LABEL_REGEX = buildLabelRegex()

const parseBlock = (block: string): TestCaseRecord => {
  const text = block.replace(/\r\n?/g, '\n')
  const matches: { key: keyof TestCaseRecord; valueStart: number; matchIndex: number }[] = []

  LABEL_REGEX.lastIndex = 0
  let m: RegExpExecArray | null
  while ((m = LABEL_REGEX.exec(text))) {
    // m[2] 是实际匹配到的标签别名
    const alias = (m[2] || '').toLowerCase()
    const key = aliasToCanonical.get(alias)
    if (!key) continue
    matches.push({ key, valueStart: m.index + m[0].length, matchIndex: m.index })
  }

  const result: TestCaseRecord = {
    name: '',
    preconditions: '',
    module: '',
    steps: '',
    expected: '',
    tags: '',
    level: '',
  }

  if (matches.length === 0) {
    // 如果没有识别到任何标签，则将整块作为“步骤描述”兜底
    result.steps = text.trim()
    return result
  }

  for (let i = 0; i < matches.length; i++) {
    const { key, valueStart } = matches[i]
    const valueEnd = i + 1 < matches.length ? matches[i + 1].matchIndex : text.length
    let value = text.slice(valueStart, valueEnd)
    // 去除首个换行和两端空白
    value = value.replace(/^\s*\n/, '').trim()
    // 规范化换行，Excel中保留多行显示
    value = value.replace(/\u00A0/g, ' ')
    ;(result as any)[key] = value
  }

  // 标签字段：去除多余空白并统一分隔符（逗号/顿号/空格）
  if (result.tags) {
    const tags = result.tags
      .replace(/[，、\s]+/g, ',')
      .split(',')
      .map((s) => s.trim())
      .filter(Boolean)
    result.tags = tags.join(', ')
  }

  return result
}

// 规范化步骤/预期里的 [1][2] 样式为换行分隔
const normalizeIndexedList = (input: string): string => {
  if (!input) return ''
  let s = input.replace(/\r\n?/g, '\n')
  // 在每个编号标记前插入换行（仅处理 [1]、[2] ... 形式，避免误伤小数等）
  const token = /\s*(\[\d+\])/g
  s = s.replace(token, (_m, g1) => `\n${String(g1).trim()}`)
  // 去掉开头多余换行
  s = s.replace(/^\n+/, '')
  // 合并多余的空行
  s = s.replace(/\n{2,}/g, '\n')
  // 行内再清理首尾空格
  s = s
    .split('\n')
    .map((l) => l.trim())
    .filter((l) => l.length > 0)
    .join('\n')
  return s
}

const parseAll = (raw: string): TestCaseRecord[] => {
  const text = stripCodeFences(raw || '')
  if (!text) return []
  const blocks = splitBlocks(text)
  const records = blocks.map(parseBlock).map((r) => ({
    ...r,
    steps: normalizeIndexedList(r.steps),
    expected: normalizeIndexedList(r.expected),
  }))
  // 过滤完全空的记录
  return records.filter((r) => Object.values(r).some((v) => (v || '').trim().length > 0))
}

const buildSchema = () => {
  return [
    { column: '用例名称', type: String, value: (x: TestCaseRecord) => x.name, width: 45 },
    { column: '前置条件', type: String, value: (x: TestCaseRecord) => x.preconditions, width: 40 },
    { column: '所属模块', type: String, value: (x: TestCaseRecord) => x.module, width: 30 },
    { column: '步骤描述', type: String, value: (x: TestCaseRecord) => x.steps, width: 60, wrap: true },
    { column: '预期结果', type: String, value: (x: TestCaseRecord) => x.expected, width: 60, wrap: true },
    { column: '编辑模式', type: String, value: () => 'STEP', width: 12 },
    { column: '标签', type: String, value: (x: TestCaseRecord) => x.tags, width: 25 },
    { column: '用例等级', type: String, value: (x: TestCaseRecord) => x.level, width: 12 },
  ] as const
}

const handleExcel = async () => {
  try {
    const text = displayContent.value
    if (!text) {
      toast.error(t('toast.error.excelNoContent'))
      return
    }
    const data = parseAll(text)
    if (!data.length) {
      toast.error(t('toast.error.excelNoData'))
      return
    }

    // 校验必填字段是否缺失
    const REQUIRED_FIELDS: (keyof TestCaseRecord)[] = ['name', 'preconditions', 'module', 'steps', 'expected', 'tags', 'level']
    const missingFieldKeys = new Set<keyof TestCaseRecord>()
    for (const r of data) {
      for (const f of REQUIRED_FIELDS) {
        if (!String((r as any)[f] ?? '').trim()) {
          missingFieldKeys.add(f)
        }
      }
    }
    if (missingFieldKeys.size > 0) {
      const fieldsText = Array.from(missingFieldKeys)
        .map((k) => t(`testcase.fields.${k}`))
        .join(', ')
      toast.error(t('toast.error.excelMissingFields', { fields: fieldsText }))
      return
    }

    const schema = buildSchema() as any
    const now = new Date()
    const pad = (n: number) => String(n).padStart(2, '0')
    const fileName = `测试用例_${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(
      now.getSeconds(),
    )}.xlsx`

    await writeXlsxFile(data as any[], {
      schema: schema as any,
      fileName,
    })
  } catch (err) {
    console.error('导出 Excel 失败:', err)
    toast.error(t('toast.error.excelExportFailed'))
  }
}

// 复制功能
const handleCopy = (type: 'content' | 'reasoning' | 'all') => {
  let textToCopy = ''
  let emitType: 'content' | 'reasoning' | 'all' = type

  switch (type) {
    case 'content':
      textToCopy = displayContent.value
      break
    case 'reasoning':
      textToCopy = displayReasoning.value
      break
    case 'all':
      textToCopy = [displayReasoning.value && `推理过程：\n${displayReasoning.value}`, `主要内容：\n${displayContent.value}`].filter(Boolean).join('\n\n')
      break
  }

  if (textToCopy) {
    copyText(textToCopy)
    emit('copy', textToCopy, emitType)
  }
}

// 全屏功能
const handleFullscreen = () => {
  emit('fullscreen')
}

// 推理内容
const toggleReasoning = () => {
  isReasoningExpanded.value = !isReasoningExpanded.value
  userHasManuallyToggledReasoning.value = true // 用户手动操作，锁定自动行为
  emit('reasoning-toggle', isReasoningExpanded.value)
}

const scrollReasoningToBottom = () => {
  if (reasoningContentRef.value) {
    nextTick(() => {
      if (reasoningContentRef.value) {
        reasoningContentRef.value.scrollTop = reasoningContentRef.value.scrollHeight
      }
    })
  }
}

// 对比功能
const updateCompareResult = async () => {
  if (internalViewMode.value === 'diff' && props.originalContent && props.content) {
    try {
      if (!props.compareService) {
        throw new Error('CompareService is required but not provided')
      }
      compareResult.value = await props.compareService.compareTexts(props.originalContent, props.content)
    } catch (error) {
      console.error('Error calculating diff:', error)
      // 重新抛出错误，让调用者处理
      throw error
    }
  } else {
    compareResult.value = undefined
  }
}

// 智能自动切换逻辑
const previousViewMode = ref<'render' | 'source' | 'diff' | null>(null)

watch(
  () => props.streaming,
  (isStreaming, wasStreaming) => {
    // --- 用户意图记忆状态机 ---
    if (isStreaming && !wasStreaming) {
      // 新任务开始，重置用户记忆
      userHasManuallyToggledReasoning.value = false
    } else if (!isStreaming && wasStreaming) {
      // 任务结束，如果用户未干预且思考区域仍然展开，自动折叠
      if (!userHasManuallyToggledReasoning.value && isReasoningExpanded.value) {
        isReasoningExpanded.value = false
      }
    }
    // -------------------------

    if (isStreaming) {
      // 记住当前模式，并强制切换到原文模式
      if (internalViewMode.value !== 'source') {
        previousViewMode.value = internalViewMode.value
        internalViewMode.value = 'source'
      }
    } else {
      // 流式结束后，恢复之前的模式
      if (previousViewMode.value) {
        internalViewMode.value = previousViewMode.value
        previousViewMode.value = null
      }
    }
  },
)

watch(internalViewMode, updateCompareResult, { immediate: true })
watch(
  () => [props.content, props.originalContent],
  () => {
    if (internalViewMode.value === 'diff') {
      updateCompareResult()
    }
  },
)

watch(
  () => props.reasoning,
  (newReasoning, oldReasoning) => {
    // 当推理内容从无到有，且用户未手动干预时，自动展开
    if (newReasoning && !oldReasoning && !userHasManuallyToggledReasoning.value) {
      isReasoningExpanded.value = true
      emit('reasoning-toggle', true)
    }

    // 如果思考过程已展开，滚动到底部
    if (isReasoningExpanded.value) {
      scrollReasoningToBottom()
    }
  },
)

watch(
  () => props.content,
  (newContent, oldContent) => {
    // 当主要内容开始流式输出时，如果用户未干预，自动折叠思考过程
    const mainContentJustStarted = newContent && !oldContent
    if (props.streaming && mainContentJustStarted && !userHasManuallyToggledReasoning.value) {
      isReasoningExpanded.value = false
    }
  },
)

// 暴露方法给父组件
const resetReasoningState = (initialState: boolean) => {
  isReasoningExpanded.value = initialState
  userHasManuallyToggledReasoning.value = false // 重置全屏状态时，也应重置用户意图
}

// 强制退出编辑状态 - 重构为强制切换到渲染模式
const forceExitEditing = () => {
  internalViewMode.value = 'render'
}

// 保持向后兼容的方法
const forceRefreshContent = () => {
  // V2版本中这个方法不再需要，但保留以确保向后兼容
}

const themeToolbarBg = 'theme-toolbar-bg'
const themeToolbarBorder = 'theme-toolbar-border'
const themeToolbarButton = 'theme-toolbar-button'
const themeToolbarButtonActive = 'theme-toolbar-button-active'

defineExpose({ resetReasoningState, forceRefreshContent, forceExitEditing })
</script>

<style scoped>
/* 顶层工具栏样式 */
.output-display-toolbar {
  @apply flex-none bg-gray-50 dark:bg-gray-800;
}

/* 推理面板标题栏样式 */
.reasoning-header {
  @apply flex-none bg-gray-50 dark:bg-gray-800;
}

.output-display__reasoning {
  @apply flex-none mt-0;
}

.reasoning-content {
  @apply overflow-y-auto mt-0;
  max-height: 30vh;
  padding: 0;
}

.streaming-indicator {
  @apply inline-flex items-center gap-1 text-blue-500;
}

.streaming-indicator::before {
  content: '';
  @apply w-2 h-2 rounded-full bg-blue-500 animate-pulse;
}

.output-display__content {
  @apply flex-1 min-h-0;
}

.loading-placeholder,
.empty-placeholder {
  @apply flex items-center justify-center h-full text-gray-500 text-sm italic;
}
</style>
