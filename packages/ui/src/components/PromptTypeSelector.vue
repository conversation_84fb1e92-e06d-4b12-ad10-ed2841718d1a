<!-- 提示词类型选择器组件 - 仅用于 system mode -->
<template>
  <div class="space-y-3">
    <!-- 多媒体内容选择 -->
    <div class="flex items-start space-x-2">
      <!-- 类型选择按钮 -->
      <div class="flex space-x-1">
        <button
          @click="selectType('prompt_plaintext')"
          :class="[
            'px-2 py-1.5 text-xs rounded border transition-colors duration-150',
            'focus:outline-none focus:ring-1 focus:ring-blue-400',
            contextConfig.contentType === 'prompt_plaintext' ? 'theme-button-primary' : 'theme-button-secondary',
            'flex items-center space-x-1',
          ]"
          :title="t('promptType.prompt_plaintext')"
        >
          <span>{{ t('promptType.prompt_plaintext') }}</span>
        </button>

        <button
          @click="selectType('prompt_url')"
          :class="[
            'px-2 py-1.5 text-xs rounded border transition-colors duration-150',
            'focus:outline-none focus:ring-1 focus:ring-blue-400',
            contextConfig.contentType === 'prompt_url' ? 'theme-button-primary' : 'theme-button-secondary',
            'flex items-center space-x-1',
          ]"
          :title="t('promptType.prompt_url')"
        >
          <span>URL</span>
        </button>

        <button
          @click="selectType('prompt_image')"
          :class="[
            'px-2 py-1.5 text-xs rounded border transition-colors duration-150',
            'focus:outline-none focus:ring-1 focus:ring-blue-400',
            contextConfig.contentType === 'prompt_image' ? 'theme-button-primary' : 'theme-button-secondary',
            'flex items-center space-x-1',
          ]"
          :title="t('promptType.prompt_image')"
        >
          <span>{{ t('promptType.prompt_image') }}</span>
        </button>
      </div>

      <!-- 内容输入区域 -->
      <div class="flex-1 min-w-[200px]">
        <!-- URL 类型：显示 URL 输入 -->
        <template v-if="contextConfig.contentType === 'prompt_url'">
          <div>
            <input
              :value="contextConfig.contents || ''"
              @input="updateUrl(($event.target as HTMLInputElement).value)"
              @keydown.enter="handleUrlEnter"
              type="url"
              :placeholder="t('promptType.urlPlaceholder')"
              class="w-full px-2 py-0.5 text-sm theme-input"
              :disabled="isLoading"
            />
          </div>
        </template>

        <!-- 图片类型：显示文件选择 -->
        <template v-else-if="contextConfig.contentType === 'prompt_image'">
          <div>
            <div class="flex items-center space-x-2">
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                @change="handleImageSelect"
                class="flex-1 px-2 py-0 text-sm theme-input"
                :disabled="isLoading"
              />
              <div v-if="contextConfig.contents" class="flex-shrink-0">
                <img
                  :src="`data:image/jpeg;base64,${contextConfig.contents}`"
                  alt="Preview"
                  class="h-8 w-8 object-contain border rounded cursor-pointer"
                  @click="showImagePreview = true"
                />
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <div
      v-if="showImagePreview && contextConfig.contents"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75"
      @click="showImagePreview = false"
    >
      <div class="relative max-w-4xl max-h-4xl p-4">
        <img :src="`data:image/jpeg;base64,${contextConfig.contents}`" alt="Preview" class="max-w-full max-h-full object-contain" />
        <button
          @click="showImagePreview = false"
          class="absolute top-2 right-2 w-8 h-8 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center hover:bg-opacity-75"
        >
          ×
        </button>
      </div>
    </div>

    <!-- 全屏加载弹窗 -->
    <div v-if="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, type PropType } from 'vue'
import { useI18n } from 'vue-i18n'
import type { ContentType } from '@prompt-optimizer/core'
import { createMarkItDownService, type MarkItDownError, getEnvVar } from '@prompt-optimizer/core'
import type { ContextConfig } from '@/composables'

const { t } = useI18n()

const props = defineProps({
  contextConfig: {
    type: Object as PropType<ContextConfig>,
    required: true,
  },
  contents: {
    type: String,
    required: true,
  },
  optimizedPrompt: {
    type: String,
    default: '',
  },
})

const emit = defineEmits<{
  'update:contextConfig': [value: ContextConfig]
  'update:contents': [value: string]
}>()

// 本地状态
const fileInput = ref<HTMLInputElement>()
const showImagePreview = ref(false)
const isLoading = ref(false)

// 创建 MarkItDown 服务实例
// 优先从运行时配置（window.runtime_config）/process.env/import.meta.env 读取
const markitdownService = createMarkItDownService({
  url: getEnvVar('VITE_MARKITDOWN_SERVER') || 'http://127.0.0.1:8500',
})

// 方法
const updatePromptContent = (value: string) => {
  emit('update:contents', value)
}

const updateUrl = (value: string) => {
  emit('update:contextConfig', { ...props.contextConfig, contents: value })
}

// URL回车处理
const handleUrlEnter = async () => {
  const url = props.contextConfig.contents?.trim()
  if (!url) return

  isLoading.value = true
  try {
    // 调用 MarkItDown API 处理 URL
    const result = await markitdownService.processUrl(url)
    emit('update:contents', result.markdown)
  } catch (error) {
    console.error('URL processing error:', error)
    const errorMessage = error instanceof Error ? error.message : '处理URL时发生未知错误'
    alert(`处理URL失败: ${errorMessage}`)
  } finally {
    isLoading.value = false
  }
}

const selectType = (type: ContentType) => {
  // 切换类型时清空相关数据
  const newData: ContextConfig = {
    ...props.contextConfig,
    contentType: type,
    contents: '',
  }

  emit('update:contextConfig', newData)
}

const handleImageSelect = async (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert(t('promptType.invalidImageType'))
      return
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(t('promptType.imageTooLarge'))
      return
    }

    isLoading.value = true

    try {
      // 调用 MarkItDown API 处理文件
      const result = await markitdownService.processFile(file)

      // 对于图片文件，需要转换为base64并存储到contextConfig
      if (props.contextConfig.contentType === 'prompt_image') {
        const base64 = await fileToBase64(file)
        emit('update:contextConfig', { ...props.contextConfig, contents: base64 })
      }

      // 将markdown内容存储到contents字段
      emit('update:contents', result.markdown)
    } catch (error) {
      console.error('File processing error:', error)
      const errorMessage = error instanceof Error ? error.message : '处理文件时发生未知错误'
      alert(`处理文件失败: ${errorMessage}`)
    } finally {
      isLoading.value = false
    }
  }
}

const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      // 移除 data:image/xxx;base64, 前缀
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    reader.onerror = reject
    reader.readAsDataURL(file)
  })
}
</script>

<style scoped>
/* 弹窗动画 */
.fixed {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 禁用状态样式 */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: var(--theme-bg-secondary, #f5f5f5);
  color: var(--theme-text-disabled, #9ca3af);
}

input[type='file']:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
