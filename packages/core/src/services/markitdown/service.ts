/**
 * MarkItDown HTTP 服务
 * 用于调用 MarkItDown API 进行文件和 URL 的 Markdown 转换
 */

import type { ApiResponse, ProcessFileResponseData, ProcessConfluenceResponseData, MarkItDownConfig, RequestOptions } from './types'
import { MarkItDownError } from './types'

export class MarkItDownService {
  private config: MarkItDownConfig
  private baseUrl: string

  constructor(config: MarkItDownConfig) {
    this.config = {
      timeout: 300000, // 默认 300 秒超时,5 分钟
      ...config,
    }
    this.baseUrl = this.config.url
  }

  /**
   * 处理文件上传并转换为 Markdown
   */
  async processFile(file: File, options?: RequestOptions): Promise<ProcessFileResponseData> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.makeRequest<ProcessFileResponseData>('POST', '/process_file', {
      body: formData,
      ...options,
    })

    return response
  }

  /**
   * 处理 Confluence URL 并转换为 Markdown
   */
  async processConfluenceUrl(url: string, options?: RequestOptions): Promise<ProcessConfluenceResponseData> {
    const searchParams = new URLSearchParams({ url })
    const endpoint = `/process_confluence_url?${searchParams.toString()}`

    const response = await this.makeRequest<ProcessConfluenceResponseData>('GET', endpoint, options)

    return response
  }

  /**
   * 通用的 URL 处理方法（目前使用 Confluence API）
   * 后续可以扩展支持其他类型的 URL
   */
  async processUrl(url: string, options?: RequestOptions): Promise<ProcessConfluenceResponseData> {
    // 目前只支持 Confluence URL，后续可以根据 URL 类型进行路由
    return this.processConfluenceUrl(url, options)
  }

  /**
   * 发起 HTTP 请求的通用方法
   */
  private async makeRequest<T>(method: 'GET' | 'POST', endpoint: string, options?: RequestOptions & { body?: FormData }): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`
    const timeout = options?.timeout ?? this.config.timeout ?? 30000

    // 创建 AbortController 用于超时控制
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    // 如果传入了外部的 AbortSignal，也要监听
    if (options?.signal) {
      options.signal.addEventListener('abort', () => controller.abort())
    }

    try {
      const fetchOptions: RequestInit = {
        method,
        signal: controller.signal,
      }

      // 只有在 POST 请求且有 body 时才添加 body
      if (method === 'POST' && options?.body) {
        fetchOptions.body = options.body
      }

      const response = await fetch(url, fetchOptions)

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new MarkItDownError(`HTTP ${response.status}: ${response.statusText}`, 'HTTP_ERROR', `请求失败: ${method} ${url}`, response.status)
      }

      const apiResponse: ApiResponse<T> = await response.json()

      if (!apiResponse.success) {
        throw new MarkItDownError(apiResponse.message || '请求失败', apiResponse.error?.code || 'API_ERROR', apiResponse.error?.details, response.status)
      }

      if (!apiResponse.data) {
        throw new MarkItDownError('服务器返回的数据为空', 'EMPTY_RESPONSE', '服务器响应中缺少 data 字段')
      }

      return apiResponse.data
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof MarkItDownError) {
        throw error
      }

      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new MarkItDownError('网络连接失败', 'NETWORK_ERROR', `无法连接到服务器: ${this.baseUrl}`)
      }

      if (error instanceof DOMException && error.name === 'AbortError') {
        throw new MarkItDownError('请求超时', 'TIMEOUT_ERROR', `请求超时 (${timeout}ms): ${method} ${url}`)
      }

      throw new MarkItDownError('未知错误', 'UNKNOWN_ERROR', error instanceof Error ? error.message : String(error))
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(options?: RequestOptions): Promise<boolean> {
    try {
      await this.makeRequest<any>('GET', '/', options)
      return true
    } catch {
      return false
    }
  }
}

/**
 * 创建 MarkItDown 服务实例的工厂函数
 */
export function createMarkItDownService(config?: Partial<MarkItDownConfig>): MarkItDownService {
  // 在 core 层不直接访问环境变量，由上层传入配置
  const defaultConfig: MarkItDownConfig = {
    url: 'http://127.0.0.1:8500',
    ...config,
  }

  return new MarkItDownService(defaultConfig)
}
