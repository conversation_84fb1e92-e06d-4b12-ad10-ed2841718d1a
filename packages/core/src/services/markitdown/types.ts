/**
 * MarkItDown API 相关类型定义
 */

// API 响应的基础结构
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: ErrorDetail
}

// 错误详情
export interface ErrorDetail {
  code: string
  message: string
  details?: string
}

// 文件处理响应数据
export interface ProcessFileResponseData {
  markdown: string
  filename?: string
  file_size?: number
  content_length?: number
}

// Confluence 页面处理响应数据
export interface ProcessConfluenceResponseData {
  markdown: string
  page_id: string
  source_url: string
  html_content_length?: number
  markdown_content_length?: number
}

// 服务配置
export interface MarkItDownConfig {
  url: string
  timeout?: number
}

// 请求选项
export interface RequestOptions {
  timeout?: number
  signal?: AbortSignal
}

// 服务错误类型
export class MarkItDownError extends Error {
  public readonly code: string
  public readonly details?: string
  public readonly statusCode?: number

  constructor(message: string, code: string = 'MARKITDOWN_ERROR', details?: string, statusCode?: number) {
    super(message)
    this.name = 'MarkItDownError'
    this.code = code
    this.details = details
    this.statusCode = statusCode
  }
}
