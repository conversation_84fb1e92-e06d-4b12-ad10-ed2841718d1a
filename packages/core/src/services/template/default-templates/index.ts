/**
 * 默认模板统一导入
 *
 * 🎯 极简设计：模板自身包含完整信息，无需额外配置
 */

// 导入所有模板
import { template as testcase_generator } from './optimize/testcase-generator'
import { template as testcase_generator_en } from './optimize/testcase-generator_en'

import { template as content_fine_tuning } from './iterate/content_fine_tuning'
import { template as content_fine_tuning_en } from './iterate/content_fine_tuning_en'
import { template as prd_optimize } from './iterate/prd-optimize'
import { template as prd_optimize_en } from './iterate/prd-optimize_en'

import { user_prompt_basic } from './user-optimize/user-prompt-basic'
import { user_prompt_basic_en } from './user-optimize/user-prompt-basic_en'

// 简单的模板集合 - 模板自身已包含完整信息（id、name、language、type等）
export const ALL_TEMPLATES = {
  testcase_generator,
  testcase_generator_en,
  content_fine_tuning,
  content_fine_tuning_en,
  prd_optimize,
  prd_optimize_en,
  user_prompt_basic,
  user_prompt_basic_en,
}
